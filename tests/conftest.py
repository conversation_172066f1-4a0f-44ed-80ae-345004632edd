"""
Test configuration and fixtures for the carbon regulation news application.
"""

import os
import pytest
import tempfile
from datetime import datetime, timezone
from typing import Generator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from fastapi.testclient import TestClient

# Set test environment before importing app modules
os.environ["ENVIRONMENT"] = "test"
os.environ["DATABASE__URL"] = "sqlite:///:memory:"
os.environ["LOG_LEVEL"] = "WARNING"

from app.core.database import Base, get_db
from app.core.models import NewsArticle, TaskExecution, TaskResult, NotificationLog
from app.core.config import get_settings
from app.api.main import app


@pytest.fixture(scope="session")
def test_settings():
    """Get test settings."""
    settings = get_settings()
    settings.environment = "test"
    settings.database.url = "sqlite:///:memory:"
    settings.database.echo = False
    settings.log_level = "WARNING"
    return settings


@pytest.fixture(scope="session")
def test_engine(test_settings):
    """Create test database engine."""
    engine = create_engine(
        test_settings.database.url,
        connect_args={"check_same_thread": False},
        echo=test_settings.database.echo
    )
    Base.metadata.create_all(bind=engine)
    return engine


@pytest.fixture(scope="session")
def test_session_factory(test_engine):
    """Create test session factory."""
    return sessionmaker(autocommit=False, autoflush=False, bind=test_engine)


@pytest.fixture
def db_session(test_session_factory) -> Generator[Session, None, None]:
    """Create a test database session."""
    session = test_session_factory()
    try:
        yield session
    finally:

        # Clean up all data to prevent constraint violations
        session.rollback()
        # Clear all tables
        for table in reversed(Base.metadata.sorted_tables):
            session.execute(table.delete())
        session.commit()
        session.close()


@pytest.fixture
def client(db_session):
    """Create a test client with database dependency override."""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture
def sample_news_article(db_session) -> NewsArticle:
    """Create a sample news article for testing."""
    import uuid
    unique_id = str(uuid.uuid4())[:8]
    article = NewsArticle(
        title="New Carbon Tax Regulation Announced",
        url=f"https://example.com/carbon-tax-news-{unique_id}",
        content="The government announced new carbon tax regulations that will take effect next year. The new policy aims to reduce emissions by 30% over the next decade.",
        source_name="Example News",
        published_date=datetime(2024, 1, 15, 10, 0, 0, tzinfo=timezone.utc),
        collected_at=datetime.now(timezone.utc),
        is_processed=False
    )

    db_session.add(article)
    db_session.commit()
    db_session.refresh(article)
    return article


@pytest.fixture
def processed_news_article(db_session) -> NewsArticle:
    """Create a processed news article with AI data for testing."""
    import uuid
    unique_id = str(uuid.uuid4())[:8]
    article = NewsArticle(
        title="EU Emissions Trading System Update",
        url=f"https://example.com/eu-ets-update-{unique_id}",
        content="The European Union announced updates to its Emissions Trading System, including new allocation rules and expanded coverage.",
        source_name="EU News",
        published_date=datetime(2024, 1, 16, 14, 30, 0, tzinfo=timezone.utc),
        collected_at=datetime.now(timezone.utc),
        is_processed=True,
        processed_at=datetime.now(timezone.utc),
        ai_classification={
            "category": "Carbon pricing & markets",
            "type": "Regulatory Update (Final)",
            "jurisdictions": ["EU"],
            "sectors": ["power", "industry"]
        },
        ai_summary="The EU has updated its ETS with new allocation rules and expanded coverage to include shipping.",
        ai_key_points=[
            "New allocation rules for free allowances",
            "Expanded coverage to maritime sector",
            "Stricter monitoring requirements"
        ]
    )

    db_session.add(article)
    db_session.commit()
    db_session.refresh(article)
    return article


@pytest.fixture
def sample_task_execution(db_session) -> TaskExecution:
    """Create a sample task execution for testing."""
    task = TaskExecution(
        task_name="test_news_collection",
        task_type="news_collection",
        status="success",
        started_at=datetime.now(timezone.utc),
        completed_at=datetime.now(timezone.utc),
        duration_seconds=45.5,
        result_summary={
            "collected_count": 10,
            "saved_count": 8,
            "duplicate_count": 2,
            "sources": ["Example News", "Test Source"]
        }
    )
    
    db_session.add(task)
    db_session.commit()
    db_session.refresh(task)
    return task


@pytest.fixture
def sample_task_result(db_session, sample_task_execution) -> TaskResult:
    """Create a sample task result for testing."""
    result = TaskResult(
        task_execution_id=sample_task_execution.id,
        result_type="daily_summary",
        title="Daily Carbon Regulation Summary - 2024-01-15",
        summary="Today's summary includes 5 new regulatory updates and 3 market developments.",
        key_findings=[
            "New carbon tax announced in Canada",
            "EU ETS prices reached record high",
            "California extends cap-and-trade program"
        ],
        statistics={
            "total_articles": 8,
            "categories": {
                "Carbon pricing & markets": 3,
                "Regulatory Update (Final)": 2,
                "Legislation": 3
            }
        },
        result_metadata={
            "executive_summary": "Significant regulatory developments in carbon pricing this week.",
            "key_developments": ["Carbon tax implementation", "ETS expansion"],
            "generated_at": datetime.now(timezone.utc).isoformat()
        }
    )
    
    db_session.add(result)
    db_session.commit()
    db_session.refresh(result)
    return result


@pytest.fixture
def sample_notification_log(db_session, sample_task_execution) -> NotificationLog:
    """Create a sample notification log for testing."""
    notification = NotificationLog(
        task_execution_id=sample_task_execution.id,
        notification_type="webhook",
        recipient="https://example.com/webhook",
        subject="Task Completed: test_news_collection",
        message="News collection task completed successfully with 8 new articles.",
        payload={
            "task_name": "test_news_collection",
            "status": "success",
            "articles_collected": 8
        },
        status="sent",
        response_code=200,
        response_message="OK"
    )
    
    db_session.add(notification)
    db_session.commit()
    db_session.refresh(notification)
    return notification


@pytest.fixture
def mock_tavily_client():
    """Mock Tavily client for testing."""
    class MockTavilyClient:
        def search(self, query, **kwargs):
            return {
                "results": [
                    {
                        "url": "https://example.com/article1",
                        "title": "Test Article 1",
                        "content": "This is test content for article 1 about carbon regulations."
                    },
                    {
                        "url": "https://example.com/article2", 
                        "title": "Test Article 2",
                        "content": "This is test content for article 2 about climate policy."
                    }
                ]
            }
        
        def extract(self, urls):
            return {
                "results": [
                    {
                        "content": "Extracted content from the URL with detailed information about carbon regulations and policy updates."
                    }
                ]
            }
    
    return MockTavilyClient()


@pytest.fixture
def mock_ai_agent():
    """Mock AI agent for testing."""
    class MockAIAgent:
        def run_sync(self, prompt):
            class MockResult:
                def __init__(self):
                    if "classification" in prompt.lower():
                        self.output = {
                            "category": "Carbon pricing & markets",
                            "type": "Regulatory Update (Final)",
                            "jurisdictions": ["US"],
                            "sectors": ["power"]
                        }
                    elif "content" in prompt.lower():
                        self.output = {
                            "title": "Test Article Title",
                            "summary": "This is a test summary of the article.",
                            "key_points": ["Point 1", "Point 2", "Point 3"],
                            "original_text": "Original article text content."
                        }
                    else:
                        self.output = "Mock AI response"
            
            return MockResult()
    
    return MockAIAgent()


@pytest.fixture
def temp_config_file():
    """Create a temporary configuration file for testing."""
    config_content = """
web_search_sources:
  - name: "Test Search"
    query: "carbon regulations test"
    time_range: "day"
    max_results: 5

specific_sources:
  - name: "Test Source"
    url: "https://example.com/news"
    time_range: "day"
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        f.write(config_content)
        temp_file = f.name
    
    yield temp_file
    
    # Cleanup
    os.unlink(temp_file)


@pytest.fixture
def mock_requests():
    """Mock requests for testing HTTP calls."""
    class MockResponse:
        def __init__(self, status_code=200, text="OK"):
            self.status_code = status_code
            self.text = text
    
    class MockRequests:
        def post(self, url, **kwargs):
            return MockResponse()
    
    return MockRequests()


# Test data constants
TEST_ARTICLE_CONTENT = """
Breaking: New Carbon Tax Legislation Passes Senate

The Senate voted 65-35 today to pass comprehensive carbon tax legislation that will impose a $50 per ton fee on carbon emissions starting January 1, 2025. The bill includes provisions for:

- Graduated tax rates based on emission intensity
- Revenue recycling through tax credits for clean energy
- Border carbon adjustments for imports
- Exemptions for small businesses under $1M revenue

Environmental groups praised the legislation while industry associations expressed concerns about competitiveness impacts. The bill now moves to the House where passage is expected next month.

Implementation will be overseen by the EPA with quarterly reporting requirements for covered entities. The Congressional Budget Office estimates the tax will generate $150 billion annually in revenue.
"""

TEST_AI_CLASSIFICATION = {
    "category": "Carbon pricing & markets",
    "type": "Legislation",
    "jurisdictions": ["US"],
    "sectors": ["power", "industry", "transport"]
}

TEST_AI_SUMMARY = "The US Senate passed carbon tax legislation imposing $50/ton fees starting 2025, with revenue recycling and border adjustments."

TEST_AI_KEY_POINTS = [
    "$50 per ton carbon tax starting January 2025",
    "Revenue recycling through clean energy tax credits", 
    "Border carbon adjustments for imports",
    "Small business exemptions under $1M revenue",
    "EPA oversight with quarterly reporting"
]
